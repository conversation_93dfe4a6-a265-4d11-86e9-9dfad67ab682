import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import jwt from 'jsonwebtoken';

// Configure your email settings here
const CAF_EMAIL = process.env.CAF_EMAIL || '<EMAIL>';
const EMAIL_USER = process.env.EMAIL_USER || '<EMAIL>';
const EMAIL_PASS = process.env.EMAIL_PASS || 'jpxjlwdlvfkceqgi';
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request) {
  try {
    // Verify admin authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Token di autenticazione mancante' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    try {
      jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { success: false, message: 'Token di autenticazione non valido' },
        { status: 401 }
      );
    }

    const data = await request.json();
    const { nome, cognome, email } = data;

    // Validate required fields
    if (!nome || !cognome || !email) {
      return NextResponse.json(
        { success: false, message: 'Tutti i campi sono obbligatori' },
        { status: 400 }
      );
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: EMAIL_USER,
        pass: EMAIL_PASS,
      },
    });

    // Get the base URL for the booking link
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const bookingUrl = `${baseUrl}`;

    // Email content for client invitation
    const invitationEmailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #252B59; color: white; padding: 20px; text-align: center;">
          <img src="${baseUrl}/public/img/logo/logo.png" alt="CAF Logo" style="max-height: 60px; margin-bottom: 10px;" />
          <h1>Invito per Prenotazione Appuntamento</h1>
        </div>
        
        <div style="padding: 20px; background-color: #F7F7F5;">
          <h2 style="color: #B42C2C;">Gentile ${nome} ${cognome},</h2>
          
          <p>La invitiamo a prenotare un appuntamento presso il nostro Centro di Assistenza Fiscale.</p>
          
          <p>Per procedere con la prenotazione, clicchi sul pulsante qui sotto:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${bookingUrl}" 
               style="background-color: #B42C2C; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block;">
              PRENOTA APPUNTAMENTO
            </a>
          </div>
          
          <p>Oppure copi e incolli questo link nel suo browser:</p>
          <p style="background-color: white; padding: 10px; border: 1px solid #D1D1D1; border-radius: 5px; word-break: break-all;">
            <a href="${bookingUrl}" style="color: #B42C2C;">${bookingUrl}</a>
          </p>
          
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>Servizi disponibili:</strong></p>
            <ul style="color: #856404; margin: 10px 0;">
              <li>Servizi CAF (ISEE, 730, CU, ecc.)</li>
              <li>Servizi Patronato</li>
              <li>Servizi Legali</li>
              <li>Servizi di Immigrazione</li>
            </ul>
          </div>
          
          <p>La ringraziamo per aver scelto i nostri servizi.</p>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #D1D1D1;">
            <p style="color: #555555; font-size: 14px;">
              Centro di Assistenza Fiscale<br>
              Per informazioni: ${CAF_EMAIL}
            </p>
          </div>
        </div>
      </div>
    `;

    // Send email to client
    await transporter.sendMail({
      from: EMAIL_USER,
      to: email,
      subject: 'Invito per Prenotazione Appuntamento CAF',
      html: invitationEmailContent,
    });

    return NextResponse.json({
      success: true,
      message: 'Link di invito inviato con successo'
    });

  } catch (error) {
    console.error('Error sending invitation email:', error);
    return NextResponse.json(
      { success: false, message: 'Errore nell\'invio dell\'email di invito' },
      { status: 500 }
    );
  }
}
